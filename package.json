{"name": "acu-student-passport", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier . --write", "format:check": "prettier . --check", "export-data": "node export-supabase-data.js"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@assistant-ui/react": "^0.10.23", "@assistant-ui/react-ai-sdk": "^0.10.13", "@assistant-ui/react-markdown": "^0.10.4", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.9.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.50.0", "@types/qrcode": "^1.5.5", "@vercel/analytics": "^1.5.0", "ai": "^4.3.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.511.0", "nanoid": "^5.1.5", "next": "15.1.8", "next-themes": "^0.4.6", "prisma": "^6.9.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "remark-gfm": "^4.0.1", "resend": "^4.5.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.64", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "^20.19.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.28.0", "eslint-config-next": "15.1.8", "postcss": "^8.5.5", "prettier": "3.5.3", "prettier-plugin-tailwindcss": "0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}