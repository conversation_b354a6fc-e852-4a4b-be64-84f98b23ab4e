# Supabase 完全移除指南

## 已完成的清理步骤

### ✅ 1. 移除源代码中的 Supabase 引用

- [x] 删除 `src/lib/supabase.ts` 文件
- [x] 更新 `FeedbackModal.tsx` 移除 Supabase 导入和使用
- [x] 清理 `career/[user_id]/page.tsx` 中的注释

### ✅ 2. 移除 package.json 依赖

- [x] 删除 `"@supabase/supabase-js": "^2.50.0"` 依赖
- [x] 删除 `"export-data": "node export-supabase-data.js"` 脚本

## 需要手动执行的清理步骤

### 🔧 3. 清理依赖和构建文件

在项目根目录执行以下命令：

```bash
# 1. 删除 node_modules 和锁定文件
rm -rf node_modules
rm pnpm-lock.yaml

# 2. 重新安装依赖（不包含 Supabase）
pnpm install

# 3. 清理 Next.js 构建缓存
rm -rf .next
rm -rf .turbo

# 4. 重新构建项目
pnpm build
```

### 🗑️ 4. 可选：删除相关文件

如果存在以下文件，可以删除：

```bash
# 删除 Supabase 数据导出脚本（如果存在）
rm -f export-supabase-data.js

# 删除环境变量中的 Supabase 配置
# 编辑 .env.local 文件，移除：
# NEXT_PUBLIC_SUPABASE_URL=...
# NEXT_PUBLIC_SUPABASE_ANON_KEY=...
```

## 验证清理结果

### 🔍 验证步骤

1. **检查依赖**：

   ```bash
   pnpm list | grep supabase
   # 应该没有输出
   ```

2. **检查代码引用**：

   ```bash
   grep -r "supabase" src/ --exclude-dir=node_modules
   # 应该没有输出或只有注释
   ```

3. **测试应用**：

   ```bash
   pnpm dev
   # 确保应用正常启动和运行
   ```

4. **测试反馈功能**：
   - 完成用户注册流程
   - 测试反馈模态框功能
   - 确认使用新的 API 端点

## 清理后的架构

### 🏗️ 新的数据库架构

- **主数据库**：Prisma + PostgreSQL
- **用户管理**：通过 `/api/users/*` 端点
- **反馈系统**：通过 `/api/users/update-feedback` 端点
- **数据一致性**：所有操作使用 Prisma

### 📁 保留的文件

- `BUGFIX_SUPABASE_FEEDBACK.md` - 作为历史记录
- `prisma/schema.prisma` - 数据库模式
- `src/lib/prisma.ts` - Prisma 客户端配置

### 🚫 已移除的内容

- `@supabase/supabase-js` 依赖
- `src/lib/supabase.ts` 文件
- Supabase 客户端调用
- 相关的环境变量引用

## 注意事项

1. **数据迁移**：如果之前有 Supabase 数据，确保已迁移到 PostgreSQL
2. **环境变量**：移除 `.env.local` 中的 Supabase 配置
3. **部署配置**：更新生产环境的环境变量
4. **监控**：确保所有功能正常工作，特别是反馈系统

## 完成确认

清理完成后，您的项目将：

- ✅ 完全移除 Supabase 依赖
- ✅ 使用统一的 Prisma + PostgreSQL 架构
- ✅ 保持所有功能正常工作
- ✅ 减少依赖复杂性
- ✅ 提高代码一致性
