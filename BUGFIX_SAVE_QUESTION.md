# Bug Fix: Missing Required Fields in saveQuestion Function

## 问题描述

用户在尝试保存问题时遇到错误："user_id, first_name, last_name, and question are required"。

### 错误发生的流程：
1. 用户在 Step4 选择问题
2. 调用 `createUserAndGenerateCareerPath` 函数
3. 在 `useUserCreation.ts` 中调用 `saveQuestion` 函数
4. `saveQuestion` 发送请求时缺少 `first_name` 和 `last_name` 参数
5. API 返回 400 错误

## 根本原因

在 `src/lib/hooks/useUserCreation.ts` 中的 `saveQuestion` 函数只传递了 `user_id` 和 `question` 参数，但 API 端点 `/api/users/save-question` 要求四个必需字段：
- `user_id`
- `first_name`
- `last_name`
- `question`

## 修复内容

### 1. 修复 useUserCreation.ts 中的 saveQuestion 函数

**修改前：**
```typescript
const saveQuestion = async (userId: string, question: string) => {
  // ...
  body: JSON.stringify({
    user_id: userId,
    question: question,
  }),
  // ...
}
```

**修改后：**
```typescript
const saveQuestion = async (
  userId: string, 
  question: string, 
  firstName: string, 
  lastName: string
) => {
  // ...
  body: JSON.stringify({
    user_id: userId,
    first_name: firstName,
    last_name: lastName,
    question: question,
  }),
  // ...
}
```

### 2. 更新函数调用

在 `createUserAndGenerateCareerPath` 函数中更新 `saveQuestion` 的调用：

```typescript
await saveQuestion(
  userId,
  question,
  userData.firstName,
  userData.lastName,
);
```

### 3. 改进 API 端点验证

使用 Zod 进行更严格的输入验证：

```typescript
const saveQuestionSchema = z.object({
  user_id: z.string().min(1, "user_id is required"),
  first_name: z.string().min(1, "first_name is required"),
  last_name: z.string().min(1, "last_name is required"),
  question: z.string().min(1, "question is required"),
});
```

### 4. 增强错误处理

- 在 `saveQuestion` 函数中添加了更详细的错误日志
- 在 `createUserAndGenerateCareerPath` 中添加了 try-catch 块，确保问题保存失败不会影响整个流程
- 改进了 API 端点的错误响应，提供更详细的验证信息

## 测试

创建了测试端点 `/api/test-save-question` 来验证修复：
- GET 请求：测试有效数据的保存
- POST 请求：测试缺少字段时的错误处理

## 影响范围

这个修复解决了以下场景中的问题：
1. 用户在 Step4 选择预设问题时
2. 用户输入自定义问题时
3. 通过 `createUserAndGenerateCareerPath` 流程保存问题时

## 验证步骤

1. 启动应用程序
2. 完成用户注册流程到 Step4
3. 选择一个问题或输入自定义问题
4. 验证问题能够成功保存到数据库
5. 检查控制台日志确认没有错误

## 注意事项

- Step5.tsx 中的 `saveQuestion` 函数已经正确实现，不需要修改
- 修复保持了向后兼容性
- 错误处理确保问题保存失败不会阻止职业路径生成
