import { prisma } from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // Test database connection by counting users
    const userCount = await prisma.user.count();
    const questionCount = await prisma.userQuestion.count();

    // Test creating a sample question (will be rolled back)
    const testUserId = "test123";
    const testQuestion = await prisma.userQuestion.create({
      data: {
        user_id: testUserId,
        first_name: "Test",
        last_name: "User",
        question: "This is a test question",
      },
    });

    // Delete the test question
    await prisma.userQuestion.delete({
      where: { id: testQuestion.id },
    });

    return NextResponse.json({
      success: true,
      message: "Database connection and schema validation successful",
      userCount,
      questionCount,
      schemaTest:
        "UserQuestion model with first_name and last_name fields working correctly",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Database connection error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Database connection failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
