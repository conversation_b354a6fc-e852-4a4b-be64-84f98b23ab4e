import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  try {
    // Test the update-feedback endpoint with valid data
    const testData = {
      user_id: "test123",
      feedback_helpful: true,
    };

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/users/update-feedback`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();

    return NextResponse.json({
      success: response.ok,
      message: response.ok 
        ? "Test passed: Feedback update endpoint working" 
        : "Test failed: API returned error",
      status: response.status,
      data: result,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Test failed: Exception occurred",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

// Test with missing fields
export async function POST() {
  try {
    // Test the update-feedback endpoint with missing fields
    const testData = {
      user_id: "test123",
      // Missing feedback_helpful field
    };

    const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/users/update-feedback`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();

    return NextResponse.json({
      success: response.status === 400, // Should return 400 for missing fields
      message: response.status === 400 
        ? "Test passed: API correctly rejected missing fields" 
        : "Test failed: API should have rejected missing fields",
      status: response.status,
      error: result,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Test failed: Exception occurred",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
