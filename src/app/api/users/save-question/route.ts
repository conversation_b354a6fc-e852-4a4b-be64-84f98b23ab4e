import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, first_name, last_name, question } = body;

    if (!user_id || !question || !first_name || !last_name) {
      return NextResponse.json(
        { error: "user_id, first_name, last_name, and question are required" },
        { status: 400 },
      );
    }

    // Insert question into database using Prisma
    await prisma.userQuestion.create({
      data: {
        user_id,
        first_name,
        last_name,
        question,
      },
    });

    console.log(
      "Question saved for user:",
      user_id,
      `(${first_name} ${last_name})`,
    );
    return NextResponse.json(
      {
        success: true,
        message: "Question saved successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
