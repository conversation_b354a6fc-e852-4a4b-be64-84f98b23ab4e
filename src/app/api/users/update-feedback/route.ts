import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

const updateFeedbackSchema = z.object({
  user_id: z.string().min(1, "user_id is required"),
  feedback_helpful: z.boolean(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body using Zod
    const validationResult = updateFeedbackSchema.safeParse(body);
    
    if (!validationResult.success) {
      const missingFields = validationResult.error.errors
        .map((err) => err.path[0])
        .join(", ");
      return NextResponse.json(
        { 
          error: `Missing or invalid fields: ${missingFields}`,
          details: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const { user_id, feedback_helpful } = validationResult.data;

    // Check if user exists first
    const existingUser = await prisma.user.findUnique({
      where: { user_id },
      select: { user_id: true, first_name: true, last_name: true },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 },
      );
    }

    // Update user feedback using Prisma
    const updatedUser = await prisma.user.update({
      where: { user_id },
      data: { feedback_helpful },
      select: {
        user_id: true,
        first_name: true,
        last_name: true,
        feedback_helpful: true,
        updated_at: true,
      },
    });

    console.log(
      "User feedback updated successfully:",
      user_id,
      `(${existingUser.first_name} ${existingUser.last_name})`,
      "Feedback:",
      feedback_helpful ? "helpful" : "not helpful"
    );

    return NextResponse.json(
      {
        success: true,
        message: "Feedback updated successfully",
        data: updatedUser,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
