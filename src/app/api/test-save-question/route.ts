import { NextRequest, NextResponse } from "next/server";

export async function GET() {
  try {
    // Test the save-question endpoint with valid data
    const testData = {
      user_id: "test123",
      first_name: "Test",
      last_name: "User",
      question: "This is a test question for validation",
    };

    const response = await fetch(
      `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/users/save-question`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testData),
      },
    );

    const result = await response.json();

    if (response.ok) {
      return NextResponse.json({
        success: true,
        message: "Test passed: Question saved successfully",
        data: result,
      });
    } else {
      return NextResponse.json({
        success: false,
        message: "Test failed: API returned error",
        error: result,
        status: response.status,
      });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Test failed: Exception occurred",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}

// Test with missing fields
export async function POST() {
  try {
    // Test the save-question endpoint with missing fields
    const testData = {
      user_id: "test123",
      question: "This is a test question with missing name fields",
      // Missing first_name and last_name
    };

    const response = await fetch(
      `${process.env.NEXTAUTH_URL || "http://localhost:3000"}/api/users/save-question`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(testData),
      },
    );

    const result = await response.json();

    return NextResponse.json({
      success: response.status === 400, // Should return 400 for missing fields
      message:
        response.status === 400
          ? "Test passed: API correctly rejected missing fields"
          : "Test failed: API should have rejected missing fields",
      status: response.status,
      error: result,
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: "Test failed: Exception occurred",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
