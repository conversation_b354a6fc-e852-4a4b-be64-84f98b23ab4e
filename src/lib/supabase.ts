import { createClient } from "@supabase/supabase-js";

// Unified Supabase client using public environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  high_school: string;
  dream_role: string;
  dream_company: string;
  selected_interests: string[];
  career_path?: Record<string, unknown>;
  qr_code?: string;
  feedback_helpful?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface UserQuestion {
  id?: number;
  user_id: string;
  question: string;
  created_at?: string;
}
