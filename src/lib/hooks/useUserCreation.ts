import { useState } from "react";
import { useCareerPath, useFormStore } from "../store";

interface CreateUserData {
  firstName: string;
  lastName: string;
  email: string;
  highSchool: string;
  dreamRole: string;
  dreamCompany: string;
  selectedInterests: string[];
}

interface UseUserCreationResult {
  createUser: (userData: CreateUserData) => Promise<string | null>;
  createUserAndGenerateCareerPath: (
    userData: CreateUserData,
    question: string,
  ) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export const useUserCreation = (): UseUserCreationResult => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const setUserId = useFormStore((state) => state.setUserId);
  const {
    setCareerPathGenerating,
    setCareerPathCompleted,
    setCareerPathError,
    setCareerPathData,
  } = useCareerPath();

  const createUser = async (
    userData: CreateUserData,
  ): Promise<string | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/users/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create user");
      }

      setIsLoading(false);
      return result.user_id;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      setIsLoading(false);
      console.error("User creation error:", err);
      return null;
    }
  };

  const saveQuestion = async (userId: string, question: string) => {
    if (!question.trim()) return;

    try {
      const response = await fetch("/api/users/save-question", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
          question: question,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        console.error("Failed to save question:", result.error);
      } else {
        console.log("Question saved successfully");
      }
    } catch (error) {
      console.error("Error saving question:", error);
    }
  };

  const generateCareerPath = async (userId: string) => {
    try {
      const response = await fetch("/api/users/generate-career-path", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: userId,
        }),
      });

      const result = await response.json();
      if (!response.ok) {
        console.error("Failed to generate career path:", result.error);
        setCareerPathError("Failed to generate career path");
      } else {
        console.log("Career path generated successfully:", result.career_path);
        setCareerPathData(result.qr_code, result.career_path_url);
        setCareerPathCompleted(true);
      }
    } catch (error) {
      console.error("Error generating career path:", error);
      setCareerPathError("Error generating career path");
    }
  };

  const createUserAndGenerateCareerPath = async (
    userData: CreateUserData,
    question: string,
  ) => {
    setCareerPathGenerating(true);
    setCareerPathError(null);

    try {
      // Create user
      const userId = await createUser(userData);
      if (!userId) {
        setCareerPathError("Failed to create user");
        return;
      }

      // Store user ID
      setUserId(userId);
      console.log("User created with ID:", userId);

      // Save question
      if (question && question.trim()) {
        await saveQuestion(userId, question);
      }

      // Generate career path
      await generateCareerPath(userId);
    } catch (error) {
      console.error("Error in background generation:", error);
      setCareerPathError("Error during background processing");
    } finally {
      setCareerPathGenerating(false);
    }
  };

  return {
    createUser,
    createUserAndGenerateCareerPath,
    isLoading,
    error,
  };
};
