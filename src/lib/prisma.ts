import { Prisma, PrismaClient } from "@prisma/client";

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma =
  globalForPrisma.prisma ??
  new PrismaClient({
    log: ["query"],
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Use Prisma generated types for better type safety
export type UserCreateInput = Prisma.UserCreateInput;
export type UserQuestionCreateInput = Prisma.UserQuestionCreateInput;
