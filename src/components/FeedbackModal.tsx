"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useFormStore } from "@/lib/store";
import { supabase } from "@/lib/supabase";
import { ThumbsDown, ThumbsUp } from "lucide-react";
import { useEffect, useState } from "react";

interface FeedbackModalProps {
  onFeedbackSubmit: () => void;
}

export default function FeedbackModal({
  onFeedbackSubmit,
}: FeedbackModalProps) {
  const isOpen = useFormStore((state) => state.isFeedbackModalOpen);
  const setOpen = useFormStore((state) => state.setFeedbackModalOpen);
  const storeFeedback = useFormStore((state) => state.careerPathFeedback);
  const setFeedback = useFormStore((state) => state.setCareerPathFeedback);
  const userId = useFormStore((state) => state.userId);
  const firstName = useFormStore((state) => state.firstName);
  const [selectedFeedback, setSelectedFeedback] = useState<
    "helpful" | "not_helpful" | null
  >(null);

  useEffect(() => {
    if (isOpen) {
      setSelectedFeedback(
        storeFeedback === "helpful"
          ? "helpful"
          : storeFeedback === "not_helpful"
            ? "not_helpful"
            : null,
      );
    }
  }, [isOpen, storeFeedback]);

  const handleFeedback = async (feedbackType: "helpful" | "not_helpful") => {
    setSelectedFeedback(feedbackType);
    setFeedback(feedbackType);

    if (userId) {
      console.log(
        "Supabase update: Attempting to update users table for userId:",
        userId,
      );
      const is_helpful_boolean = feedbackType === "helpful";
      try {
        const { data, error: supabaseError } = await supabase
          .from("users")
          .update({ feedback_helpful: is_helpful_boolean })
          .eq("user_id", userId);

        if (supabaseError) {
          console.error(
            "Error updating user feedback in Supabase:",
            supabaseError,
          );
          console.error(
            "Details of Supabase error:",
            JSON.stringify(supabaseError, null, 2),
          );
        } else {
          console.log(
            "User feedback updated successfully in Supabase. Response data:",
            data,
          );
        }
      } catch (e) {
        console.error("Exception when updating user feedback:", e);
      }
    } else {
      console.warn(
        "No userId available, skipping user feedback update to Supabase.",
      );
    }

    setOpen(false);
    onFeedbackSubmit();
  };

  const onOpenChange = (open: boolean) => {
    if (!open && !selectedFeedback && isOpen) {
      return;
    }
    setOpen(open);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        onPointerDownOutside={(e) => {
          if (isOpen && !selectedFeedback) e.preventDefault();
        }}
        onEscapeKeyDown={(e) => {
          if (isOpen && !selectedFeedback) e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            One last thing, {firstName}!
          </DialogTitle>
          <DialogDescription>How was the AI Chatbot?</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="flex justify-around items-center gap-4">
            <Button
              variant={selectedFeedback === "helpful" ? "default" : "outline"}
              size="lg"
              className={`flex-1 gap-2 items-center transition-all duration-150 ease-in-out transform hover:scale-105 ${selectedFeedback === "helpful" ? "bg-green-500 hover:bg-green-600 text-white" : "border-green-500 text-green-500 hover:bg-green-500/10"}`}
              onClick={() => handleFeedback("helpful")}
            >
              <ThumbsUp className="h-6 w-6" />
              <span>Helpful</span>
            </Button>
            <Button
              variant={
                selectedFeedback === "not_helpful" ? "default" : "outline"
              }
              size="lg"
              className={`flex-1 gap-2 items-center transition-all duration-150 ease-in-out transform hover:scale-105 ${selectedFeedback === "not_helpful" ? "bg-red-500 hover:bg-red-600 text-white" : "border-red-500 text-red-500 hover:bg-red-500/10"}`}
              onClick={() => handleFeedback("not_helpful")}
            >
              <ThumbsDown className="h-6 w-6" />
              <span>Not Helpful</span>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
