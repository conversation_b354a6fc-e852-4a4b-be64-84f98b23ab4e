// Simple test script to verify database migration
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testMigration() {
  try {
    console.log('🔍 Testing database connection...');
    
    // Test basic connection
    const userCount = await prisma.user.count();
    console.log(`✅ Users table accessible. Current count: ${userCount}`);
    
    const questionCount = await prisma.userQuestion.count();
    console.log(`✅ UserQuestions table accessible. Current count: ${questionCount}`);
    
    // Test creating a user question with new schema
    console.log('🧪 Testing UserQuestion creation with new schema...');
    const testQuestion = await prisma.userQuestion.create({
      data: {
        user_id: 'test123',
        first_name: 'Test',
        last_name: 'User',
        question: 'This is a test question to verify the new schema'
      }
    });
    
    console.log('✅ UserQuestion created successfully with new schema:', {
      id: testQuestion.id,
      user_id: testQuestion.user_id,
      first_name: testQuestion.first_name,
      last_name: testQuestion.last_name,
      question: testQuestion.question
    });
    
    // Clean up test data
    await prisma.userQuestion.delete({
      where: { id: testQuestion.id }
    });
    console.log('🧹 Test data cleaned up');
    
    console.log('🎉 Migration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testMigration();
